// Simple file upload service using multiple methods for reliable file sharing

/**
 * Upload file and return a clean downloadable link
 */
export const uploadFile = async (
  file: File,
  fileName?: string
): Promise<{ success: boolean; downloadUrl?: string; error?: string }> => {
  console.log(`🚀 Uploading file: ${fileName || file.name} (${(file.size / 1024).toFixed(2)} KB)`);

  // Try multiple upload services in order
  const uploadMethods = [
    () => uploadToFileIo(file),
    () => uploadTo0x0(file),
    () => uploadToTmpFiles(file)
  ];

  for (const uploadMethod of uploadMethods) {
    try {
      const result = await uploadMethod();
      if (result.success && result.downloadUrl) {
        console.log(`✅ File uploaded successfully: ${fileName || file.name}`);
        console.log(`🔗 Download URL: ${result.downloadUrl}`);
        return result;
      }
    } catch (error) {
      console.log(`❌ Upload method failed:`, error);
      continue;
    }
  }

  // If all external services fail, return error
  console.error('❌ All upload services failed');
  return {
    success: false,
    error: 'All file upload services are currently unavailable. Please try again later.'
  };
};

/**
 * Upload to file.io
 */
const uploadToFileIo = async (file: File): Promise<{ success: boolean; downloadUrl?: string; error?: string }> => {
  console.log('📤 Trying file.io...');

  const formData = new FormData();
  formData.append('file', file);

  const response = await fetch('https://file.io', {
    method: 'POST',
    body: formData
  });

  if (response.ok) {
    const result = await response.json();
    if (result.success && result.link) {
      return { success: true, downloadUrl: result.link };
    }
  }

  throw new Error('File.io upload failed');
};

/**
 * Upload to 0x0.st
 */
const uploadTo0x0 = async (file: File): Promise<{ success: boolean; downloadUrl?: string; error?: string }> => {
  console.log('📤 Trying 0x0.st...');

  const formData = new FormData();
  formData.append('file', file);

  const response = await fetch('https://0x0.st', {
    method: 'POST',
    body: formData
  });

  if (response.ok) {
    const downloadUrl = await response.text();
    if (downloadUrl && downloadUrl.startsWith('https://')) {
      return { success: true, downloadUrl: downloadUrl.trim() };
    }
  }

  throw new Error('0x0.st upload failed');
};

/**
 * Upload to tmpfiles.org
 */
const uploadToTmpFiles = async (file: File): Promise<{ success: boolean; downloadUrl?: string; error?: string }> => {
  console.log('📤 Trying tmpfiles.org...');

  const formData = new FormData();
  formData.append('file', file);

  const response = await fetch('https://tmpfiles.org/api/v1/upload', {
    method: 'POST',
    body: formData
  });

  if (response.ok) {
    const result = await response.json();
    if (result.status === 'success' && result.data && result.data.url) {
      const downloadUrl = result.data.url.replace('tmpfiles.org/', 'tmpfiles.org/dl/');
      return { success: true, downloadUrl };
    }
  }

  throw new Error('tmpfiles.org upload failed');
};



/**
 * Test the file upload service
 */
export const testFileUploadService = async (): Promise<void> => {
  console.log('🧪 Testing file upload services...');

  // Create a small test file
  const testContent = 'This is a test file for Defendly file upload service.';
  const testFile = new File([testContent], 'test.txt', { type: 'text/plain' });

  console.log('📤 Testing with file:', testFile.name, `(${testFile.size} bytes)`);

  // Test upload
  const result = await uploadFile(testFile);
  if (result.success) {
    console.log('✅ Upload test successful!');
    console.log('🔗 Download URL:', result.downloadUrl);
  } else {
    console.log('❌ Upload test failed:', result.error);
  }
};
