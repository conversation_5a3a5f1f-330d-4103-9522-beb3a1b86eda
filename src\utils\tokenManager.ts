interface TokenData {
  token: string;
  expiresAt: number;
  refreshToken?: string;
}

class TokenManager {
  private static readonly TOKEN_KEY = 'auth_token_data';
  private static readonly REFRESH_THRESHOLD = 5 * 60 * 1000; // 5 minutes before expiry

  static setToken(token: string, expiresIn?: number, refreshToken?: string): void {
    const expiresAt = expiresIn 
      ? Date.now() + (expiresIn * 1000) 
      : Date.now() + (24 * 60 * 60 * 1000); // Default 24 hours

    const tokenData: TokenData = {
      token,
      expiresAt,
      refreshToken
    };

    try {
      localStorage.setItem(this.TOKEN_KEY, JSON.stringify(tokenData));
    } catch (error) {
      console.error('Failed to store token:', error);
    }
  }

  static getToken(): string | null {
    try {
      const tokenDataStr = localStorage.getItem(this.TOKEN_KEY);
      if (!tokenDataStr) return null;

      const tokenData: TokenData = JSON.parse(tokenDataStr);
      
      // Check if token is expired
      if (Date.now() >= tokenData.expiresAt) {
        this.clearToken();
        return null;
      }

      return tokenData.token;
    } catch (error) {
      console.error('Failed to retrieve token:', error);
      this.clearToken();
      return null;
    }
  }

  static isTokenExpired(): boolean {
    try {
      const tokenDataStr = localStorage.getItem(this.TOKEN_KEY);
      if (!tokenDataStr) return true;

      const tokenData: TokenData = JSON.parse(tokenDataStr);
      return Date.now() >= tokenData.expiresAt;
    } catch {
      return true;
    }
  }

  static shouldRefreshToken(): boolean {
    try {
      const tokenDataStr = localStorage.getItem(this.TOKEN_KEY);
      if (!tokenDataStr) return false;

      const tokenData: TokenData = JSON.parse(tokenDataStr);
      return Date.now() >= (tokenData.expiresAt - this.REFRESH_THRESHOLD);
    } catch {
      return false;
    }
  }

  static getRefreshToken(): string | null {
    try {
      const tokenDataStr = localStorage.getItem(this.TOKEN_KEY);
      if (!tokenDataStr) return null;

      const tokenData: TokenData = JSON.parse(tokenDataStr);
      return tokenData.refreshToken || null;
    } catch {
      return null;
    }
  }

  static clearToken(): void {
    try {
      localStorage.removeItem(this.TOKEN_KEY);
      localStorage.removeItem('user_data'); // Also clear user data
    } catch (error) {
      console.error('Failed to clear token:', error);
    }
  }

  static getTokenExpiryTime(): number | null {
    try {
      const tokenDataStr = localStorage.getItem(this.TOKEN_KEY);
      if (!tokenDataStr) return null;

      const tokenData: TokenData = JSON.parse(tokenDataStr);
      return tokenData.expiresAt;
    } catch {
      return null;
    }
  }
}

export default TokenManager;
