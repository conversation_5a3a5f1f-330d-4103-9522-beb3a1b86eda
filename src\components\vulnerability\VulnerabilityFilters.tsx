import React, { useState, useRef, useEffect } from 'react';
import { ChevronDown } from 'lucide-react';

interface FilterState {
  organization: string;
  target: string;
  scanType: string;
  vulnerabilityType: string;
  severity: string;
  dateRange: string;
  status: string;
  attackVector: string;
}

interface Props {
  filters: FilterState;
  onFilterChange: (key: keyof FilterState, value: string) => void;
  onSubmit: () => void;
  onReset: () => void;
}

// Custom Dropdown Component
const CustomDropdown: React.FC<{
  value: string;
  onChange: (value: string) => void;
  options: { value: string; label: string }[];
  placeholder?: string;
}> = ({ value, onChange, options, placeholder }) => {
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  return (
    <div className="relative" ref={dropdownRef}>
      <button
        type="button"
        onClick={() => setIsOpen(!isOpen)}
        className={`w-full bg-white dark:bg-[#2A2A2A] border border-neutral-300 dark:border-neutral-600 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-[#00457F] focus:border-transparent transition-all duration-200 hover:border-[#00457F] hover:shadow-sm text-left ${
          value === '' ? 'text-neutral-400 dark:text-neutral-500' : 'text-neutral-800 dark:text-neutral-200'
        }`}
      >
        {value === '' ? placeholder : options.find(opt => opt.value === value)?.label}
        <ChevronDown className={`absolute right-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-neutral-400 transition-transform ${isOpen ? 'rotate-180' : ''}`} />
      </button>

      {isOpen && (
        <div className="absolute z-[9999] w-full mt-1 bg-white dark:bg-[#2A2A2A] border border-neutral-300 dark:border-neutral-600 rounded-md shadow-lg max-h-60 overflow-auto">
          {options.map((option, index) => (
            <button
              key={option.value}
              type="button"
              onClick={() => {
                onChange(option.value);
                setIsOpen(false);
              }}
              className={`w-full text-left px-3 py-2 text-sm transition-colors ${
                index === 0
                  ? 'text-neutral-400 dark:text-neutral-500 hover:bg-[#00457F] hover:text-white'
                  : 'text-neutral-800 dark:text-neutral-200 hover:bg-[#00457F] hover:text-white'
              } ${
                option.value === value && value !== ''
                  ? 'bg-[#00457F] text-white'
                  : 'hover:bg-[#00457F] hover:text-white'
              }`}
            >
              {option.label}
            </button>
          ))}
        </div>
      )}
    </div>
  );
};

const VulnerabilityFilters: React.FC<Props> = ({ filters, onFilterChange, onSubmit, onReset }) => {
  const filterOptions = {
    organization: [
      { value: '', label: 'By Default Set' },
      { value: 'org1', label: 'Organization 1' },
      { value: 'org2', label: 'Organization 2' },
      { value: 'org3', label: 'Organization 3' },
    ],
    target: [
      { value: '', label: 'Select Target' },
      { value: 'web-app', label: 'Web Application' },
      { value: 'api', label: 'API Endpoints' },
      { value: 'network', label: 'Network Infrastructure' },
      { value: 'mobile', label: 'Mobile Application' },
    ],
    scanType: [
      { value: '', label: 'Select Scan Type' },
      { value: 'ai-scan', label: 'AI Scan' },
      { value: 'advanced-testing', label: 'Advanced Testing' },
      { value: 'penetration-test', label: 'Penetration Test' },
      { value: 'vulnerability-assessment', label: 'Vulnerability Assessment' },
    ],
    vulnerabilityType: [
      { value: '', label: 'Vulnerability Type' },
      { value: 'sql-injection', label: 'SQL Injection' },
      { value: 'xss', label: 'Cross-Site Scripting' },
      { value: 'csrf', label: 'Cross-Site Request Forgery' },
      { value: 'authentication', label: 'Authentication Issues' },
      { value: 'authorization', label: 'Authorization Issues' },
      { value: 'information-disclosure', label: 'Information Disclosure' },
    ],
    severity: [
      { value: '', label: 'Select Severity' },
      { value: 'critical', label: 'Critical' },
      { value: 'high', label: 'High' },
      { value: 'medium', label: 'Medium' },
      { value: 'low', label: 'Low' },
      { value: 'info', label: 'Info' },
    ],
    dateRange: [
      { value: '', label: 'Select Date and Time' },
      { value: 'today', label: 'Today' },
      { value: 'yesterday', label: 'Yesterday' },
      { value: 'last-7-days', label: 'Last 7 Days' },
      { value: 'last-30-days', label: 'Last 30 Days' },
      { value: 'last-90-days', label: 'Last 90 Days' },
      { value: 'custom', label: 'Custom Range' },
    ],
    status: [
      { value: '', label: 'Select Status' },
      { value: 'open', label: 'Open' },
      { value: 'in-progress', label: 'In Progress' },
      { value: 'resolved', label: 'Resolved' },
      { value: 'false-positive', label: 'False Positive' },
      { value: 'accepted-risk', label: 'Accepted Risk' },
    ],
    attackVector: [
      { value: '', label: 'Select Attack Vector' },
      { value: 'network', label: 'Network' },
      { value: 'adjacent', label: 'Adjacent Network' },
      { value: 'local', label: 'Local' },
      { value: 'physical', label: 'Physical' },
    ],
  };

  const renderDropdown = (
    key: keyof FilterState,
    label: string,
    options: { value: string; label: string }[]
  ) => (
    <div className="flex flex-col space-y-2">
      <label className="text-sm font-medium text-neutral-700 dark:text-neutral-300">{label}</label>
      <CustomDropdown
        value={filters[key]}
        onChange={(value) => onFilterChange(key, value)}
        options={options}
        placeholder={options[0]?.label}
      />
    </div>
  );

  return (
    <div className="h-full">
      <div className="grid grid-cols-3 gap-4">
        {/* Row 1 */}
        {renderDropdown('organization', 'Organization', filterOptions.organization)}
        {renderDropdown('target', 'Target', filterOptions.target)}
        {renderDropdown('scanType', 'Scan Type', filterOptions.scanType)}

        {/* Row 2 */}
        {renderDropdown('vulnerabilityType', 'Vulnerability', filterOptions.vulnerabilityType)}
        {renderDropdown('severity', 'Severity', filterOptions.severity)}
        {renderDropdown('attackVector', 'Attack Vector', filterOptions.attackVector)}

        {/* Row 3 */}
        {renderDropdown('dateRange', 'Date', filterOptions.dateRange)}
        {renderDropdown('status', 'Status', filterOptions.status)}
        <div></div> {/* Empty cell for alignment */}
      </div>

      {/* Action Buttons */}
      <div className="flex justify-end gap-4 mt-8">
        <button
          onClick={onReset}
          className="px-6 py-2 text-sm font-medium text-neutral-700 dark:text-neutral-300 bg-transparent border border-neutral-300 dark:border-neutral-600 rounded-md hover:bg-neutral-100 dark:hover:bg-neutral-800 hover:border-neutral-400 dark:hover:border-neutral-500 transition-colors"
        >
          Reset
        </button>
        <button
          onClick={onSubmit}
          className="px-6 py-2 text-sm font-medium text-white bg-[#00457F] rounded-md hover:bg-[#00345c] transition-colors"
        >
          Submit
        </button>
      </div>
    </div>
  );
};

export default VulnerabilityFilters;
