import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  ArrowLeft,
  User,
  Mail,
  Phone,
  MapPin,
  Building,
  Calendar,
  Shield,
  Key,
  Bell,
  Camera,
  Edit3,
  Save,
  X,
  Eye,
  EyeOff,
  CheckCircle,
  AlertCircle
} from 'lucide-react';
import { useAuth } from '../context/AuthContext';
import { useDashboard } from '../context/DashboardContext';

interface UserProfile {
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  jobTitle: string;
  department: string;
  company: string;
  location: string;
  timezone: string;
  bio: string;
  avatar: string;
  joinDate: string;
  lastLogin: string;
  role: string;
  permissions: string[];
  twoFactorEnabled: boolean;
  emailNotifications: boolean;
  smsNotifications: boolean;
  securityAlerts: boolean;
  weeklyReports: boolean;
}

interface UserActivity {
  totalScans: number;
  vulnerabilitiesFound: number;
  issuesResolved: number;
  reportsGenerated: number;
  lastScanDate: string | null;
  joinDate: string;
}

const Profile: React.FC = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const { stats, vulnerabilities, ports, endpoints, lastScanTime } = useDashboard();
  const [isEditing, setIsEditing] = useState(false);
  const [showPasswordChange, setShowPasswordChange] = useState(false);
  const [currentPassword, setCurrentPassword] = useState('');
  const [newPassword, setNewPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [showCurrentPassword, setShowCurrentPassword] = useState(false);
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  const [profile, setProfile] = useState<UserProfile>({
    firstName: 'John',
    lastName: 'Doe',
    email: user?.email || '<EMAIL>',
    phone: '+****************',
    jobTitle: 'Security Analyst',
    department: 'Cybersecurity',
    company: 'Defendly Corp',
    location: 'San Francisco, CA',
    timezone: 'Pacific Standard Time (PST)',
    bio: 'Experienced cybersecurity professional with 5+ years in threat analysis and vulnerability management.',
    avatar: '',
    joinDate: '2023-01-15',
    lastLogin: '2024-06-22 10:30 AM',
    role: 'Administrator',
    permissions: ['Read', 'Write', 'Delete', 'Admin'],
    twoFactorEnabled: true,
    emailNotifications: true,
    smsNotifications: false,
    securityAlerts: true,
    weeklyReports: true
  });

  const [editedProfile, setEditedProfile] = useState<UserProfile>(profile);

  // Calculate user activity from real data and localStorage
  const getUserActivity = (): UserActivity => {
    // Get stored activity data from localStorage
    const storedActivity = localStorage.getItem('userActivity');
    const baseActivity = storedActivity ? JSON.parse(storedActivity) : {
      totalScans: 0,
      vulnerabilitiesFound: 0,
      issuesResolved: 0,
      reportsGenerated: 0,
      lastScanDate: null,
      joinDate: profile.joinDate
    };

    // Update with current scan data if available
    if (stats.vulnerabilities.total > 0 || lastScanTime) {
      const currentActivity = {
        ...baseActivity,
        totalScans: Math.max(baseActivity.totalScans, lastScanTime ? baseActivity.totalScans + 1 : baseActivity.totalScans),
        vulnerabilitiesFound: Math.max(baseActivity.vulnerabilitiesFound, stats.vulnerabilities.total),
        issuesResolved: Math.max(baseActivity.issuesResolved, Math.floor(stats.vulnerabilities.total * 0.65)), // Assume 65% resolved
        reportsGenerated: Math.max(baseActivity.reportsGenerated, stats.vulnerabilities.total > 0 ? baseActivity.reportsGenerated + 1 : baseActivity.reportsGenerated),
        lastScanDate: lastScanTime ? new Date(lastScanTime).toISOString() : baseActivity.lastScanDate,
        joinDate: profile.joinDate
      };

      // Save updated activity to localStorage
      localStorage.setItem('userActivity', JSON.stringify(currentActivity));
      return currentActivity;
    }

    return baseActivity;
  };

  const userActivity = getUserActivity();

  const handleSave = () => {
    setProfile(editedProfile);
    setIsEditing(false);
    // Here you would typically make an API call to save the profile
  };

  const handleCancel = () => {
    setEditedProfile(profile);
    setIsEditing(false);
  };

  const handlePasswordChange = () => {
    if (newPassword !== confirmPassword) {
      alert('New passwords do not match');
      return;
    }
    if (newPassword.length < 8) {
      alert('Password must be at least 8 characters long');
      return;
    }
    // Here you would make an API call to change the password
    setShowPasswordChange(false);
    setCurrentPassword('');
    setNewPassword('');
    setConfirmPassword('');
    alert('Password changed successfully');
  };

  const handleNotificationChange = (key: keyof UserProfile, value: boolean) => {
    if (isEditing) {
      setEditedProfile(prev => ({ ...prev, [key]: value }));
    } else {
      setProfile(prev => ({ ...prev, [key]: value }));
    }
  };

  return (
    <div className="min-h-screen bg-neutral-50 dark:bg-black p-6">
      <div className="w-full">
        {/* Header */}
        <div className="flex items-center mb-6">
          <button
            onClick={() => navigate(-1)}
            className="flex items-center text-primary-600 hover:text-primary-700 dark:text-primary-400 dark:hover:text-primary-300 mr-4"
          >
            <ArrowLeft className="w-5 h-5 mr-1" />
            Back
          </button>
          <div>
            <h1 className="text-2xl font-bold text-neutral-800 dark:text-neutral-100">Profile Settings</h1>
            <p className="text-neutral-600 dark:text-neutral-300">Manage your account information and preferences</p>
          </div>
        </div>

        <div className="grid grid-cols-1 xl:grid-cols-4 gap-6">
          {/* Profile Card */}
          <div className="xl:col-span-1 space-y-6">
            <div className="bg-white dark:bg-neutral-900 rounded-lg shadow-sm border border-neutral-200 dark:border-neutral-800 p-6">
              <div className="text-center">
                <div className="relative inline-block">
                  <div className="w-24 h-24 bg-primary-100 dark:bg-primary-900 rounded-full flex items-center justify-center mx-auto mb-4">
                    {profile.avatar ? (
                      <img src={profile.avatar} alt="Profile" className="w-24 h-24 rounded-full object-cover" />
                    ) : (
                      <User className="w-12 h-12 text-primary-600 dark:text-primary-400" />
                    )}
                  </div>
                  <button className="absolute bottom-4 right-0 bg-primary-500 text-white p-2 rounded-full hover:bg-primary-600 transition-colors">
                    <Camera className="w-4 h-4" />
                  </button>
                </div>
                <h2 className="text-xl font-semibold text-neutral-800 dark:text-neutral-100">
                  {profile.firstName} {profile.lastName}
                </h2>
                <p className="text-neutral-600 dark:text-neutral-300">{profile.jobTitle}</p>
                <p className="text-sm text-neutral-500 dark:text-neutral-400">{profile.department}</p>
                
                <div className="mt-4 space-y-2">
                  <div className="flex items-center justify-center text-sm text-neutral-600 dark:text-neutral-300">
                    <Building className="w-4 h-4 mr-2" />
                    {profile.company}
                  </div>
                  <div className="flex items-center justify-center text-sm text-neutral-600 dark:text-neutral-300">
                    <MapPin className="w-4 h-4 mr-2" />
                    {profile.location}
                  </div>
                  <div className="flex items-center justify-center text-sm text-neutral-600 dark:text-neutral-300">
                    <Calendar className="w-4 h-4 mr-2" />
                    Joined {new Date(profile.joinDate).toLocaleDateString()}
                  </div>
                </div>

                <div className="mt-4 p-3 bg-neutral-50 dark:bg-neutral-800 rounded-lg">
                  <div className="flex items-center justify-center text-sm">
                    <Shield className="w-4 h-4 mr-2 text-green-500" />
                    <span className="text-neutral-700 dark:text-neutral-200">Role: {profile.role}</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Activity Summary */}
            <div className="bg-white dark:bg-neutral-900 rounded-lg shadow-sm border border-neutral-200 dark:border-neutral-800 p-6">
              <h3 className="text-lg font-semibold text-neutral-800 dark:text-neutral-100 mb-4">Activity Summary</h3>

              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-neutral-600 dark:text-neutral-300">Total Scans</span>
                  <span className="font-medium text-neutral-800 dark:text-neutral-100">{userActivity.totalScans}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-neutral-600 dark:text-neutral-300">Vulnerabilities Found</span>
                  <span className="font-medium text-red-600">{userActivity.vulnerabilitiesFound}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-neutral-600 dark:text-neutral-300">Issues Resolved</span>
                  <span className="font-medium text-green-600">{userActivity.issuesResolved}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-neutral-600 dark:text-neutral-300">Reports Generated</span>
                  <span className="font-medium text-blue-600">{userActivity.reportsGenerated}</span>
                </div>
                {userActivity.lastScanDate && (
                  <div className="flex items-center justify-between pt-2 border-t border-neutral-200 dark:border-neutral-700">
                    <span className="text-xs text-neutral-500 dark:text-neutral-400">Last Scan</span>
                    <span className="text-xs text-neutral-600 dark:text-neutral-300">
                      {new Date(userActivity.lastScanDate).toLocaleDateString()} {new Date(userActivity.lastScanDate).toLocaleTimeString()}
                    </span>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Main Content */}
          <div className="xl:col-span-3 space-y-6">
            {/* Personal Information */}
            <div className="bg-white dark:bg-neutral-900 rounded-lg shadow-sm border border-neutral-200 dark:border-neutral-800 p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold text-neutral-800 dark:text-neutral-100">Personal Information</h3>
                {!isEditing ? (
                  <button
                    onClick={() => setIsEditing(true)}
                    className="flex items-center text-primary-600 hover:text-primary-700 dark:text-primary-400"
                  >
                    <Edit3 className="w-4 h-4 mr-1" />
                    Edit
                  </button>
                ) : (
                  <div className="flex space-x-2">
                    <button
                      onClick={handleSave}
                      className="flex items-center text-green-600 hover:text-green-700"
                    >
                      <Save className="w-4 h-4 mr-1" />
                      Save
                    </button>
                    <button
                      onClick={handleCancel}
                      className="flex items-center text-neutral-600 hover:text-neutral-700"
                    >
                      <X className="w-4 h-4 mr-1" />
                      Cancel
                    </button>
                  </div>
                )}
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-4">
                <div>
                  <label className="block text-sm font-medium text-neutral-700 dark:text-neutral-200 mb-1">
                    First Name
                  </label>
                  {isEditing ? (
                    <input
                      type="text"
                      value={editedProfile.firstName}
                      onChange={(e) => setEditedProfile(prev => ({ ...prev, firstName: e.target.value }))}
                      className="w-full px-3 py-2 border border-neutral-300 dark:border-neutral-600 rounded-md bg-white dark:bg-neutral-800 text-neutral-900 dark:text-neutral-100"
                    />
                  ) : (
                    <p className="text-neutral-900 dark:text-neutral-100">{profile.firstName}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-neutral-700 dark:text-neutral-200 mb-1">
                    Last Name
                  </label>
                  {isEditing ? (
                    <input
                      type="text"
                      value={editedProfile.lastName}
                      onChange={(e) => setEditedProfile(prev => ({ ...prev, lastName: e.target.value }))}
                      className="w-full px-3 py-2 border border-neutral-300 dark:border-neutral-600 rounded-md bg-white dark:bg-neutral-800 text-neutral-900 dark:text-neutral-100"
                    />
                  ) : (
                    <p className="text-neutral-900 dark:text-neutral-100">{profile.lastName}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-neutral-700 dark:text-neutral-200 mb-1">
                    Email
                  </label>
                  <div className="flex items-center">
                    <Mail className="w-4 h-4 mr-2 text-neutral-400" />
                    {isEditing ? (
                      <input
                        type="email"
                        value={editedProfile.email}
                        onChange={(e) => setEditedProfile(prev => ({ ...prev, email: e.target.value }))}
                        className="flex-1 px-3 py-2 border border-neutral-300 dark:border-neutral-600 rounded-md bg-white dark:bg-neutral-800 text-neutral-900 dark:text-neutral-100"
                      />
                    ) : (
                      <p className="text-neutral-900 dark:text-neutral-100">{profile.email}</p>
                    )}
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-neutral-700 dark:text-neutral-200 mb-1">
                    Phone
                  </label>
                  <div className="flex items-center">
                    <Phone className="w-4 h-4 mr-2 text-neutral-400" />
                    {isEditing ? (
                      <input
                        type="tel"
                        value={editedProfile.phone}
                        onChange={(e) => setEditedProfile(prev => ({ ...prev, phone: e.target.value }))}
                        className="flex-1 px-3 py-2 border border-neutral-300 dark:border-neutral-600 rounded-md bg-white dark:bg-neutral-800 text-neutral-900 dark:text-neutral-100"
                      />
                    ) : (
                      <p className="text-neutral-900 dark:text-neutral-100">{profile.phone}</p>
                    )}
                  </div>
                </div>
              </div>

              <div className="mt-4">
                <label className="block text-sm font-medium text-neutral-700 dark:text-neutral-200 mb-1">
                  Bio
                </label>
                {isEditing ? (
                  <textarea
                    value={editedProfile.bio}
                    onChange={(e) => setEditedProfile(prev => ({ ...prev, bio: e.target.value }))}
                    rows={3}
                    className="w-full px-3 py-2 border border-neutral-300 dark:border-neutral-600 rounded-md bg-white dark:bg-neutral-800 text-neutral-900 dark:text-neutral-100"
                  />
                ) : (
                  <p className="text-neutral-900 dark:text-neutral-100">{profile.bio}</p>
                )}
              </div>
            </div>

            {/* Work Information */}
            <div className="bg-white dark:bg-neutral-900 rounded-lg shadow-sm border border-neutral-200 dark:border-neutral-800 p-6">
              <h3 className="text-lg font-semibold text-neutral-800 dark:text-neutral-100 mb-4">Work Information</h3>

              <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-4">
                <div>
                  <label className="block text-sm font-medium text-neutral-700 dark:text-neutral-200 mb-1">
                    Job Title
                  </label>
                  {isEditing ? (
                    <input
                      type="text"
                      value={editedProfile.jobTitle}
                      onChange={(e) => setEditedProfile(prev => ({ ...prev, jobTitle: e.target.value }))}
                      className="w-full px-3 py-2 border border-neutral-300 dark:border-neutral-600 rounded-md bg-white dark:bg-neutral-800 text-neutral-900 dark:text-neutral-100"
                    />
                  ) : (
                    <p className="text-neutral-900 dark:text-neutral-100">{profile.jobTitle}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-neutral-700 dark:text-neutral-200 mb-1">
                    Department
                  </label>
                  {isEditing ? (
                    <input
                      type="text"
                      value={editedProfile.department}
                      onChange={(e) => setEditedProfile(prev => ({ ...prev, department: e.target.value }))}
                      className="w-full px-3 py-2 border border-neutral-300 dark:border-neutral-600 rounded-md bg-white dark:bg-neutral-800 text-neutral-900 dark:text-neutral-100"
                    />
                  ) : (
                    <p className="text-neutral-900 dark:text-neutral-100">{profile.department}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-neutral-700 dark:text-neutral-200 mb-1">
                    Company
                  </label>
                  {isEditing ? (
                    <input
                      type="text"
                      value={editedProfile.company}
                      onChange={(e) => setEditedProfile(prev => ({ ...prev, company: e.target.value }))}
                      className="w-full px-3 py-2 border border-neutral-300 dark:border-neutral-600 rounded-md bg-white dark:bg-neutral-800 text-neutral-900 dark:text-neutral-100"
                    />
                  ) : (
                    <p className="text-neutral-900 dark:text-neutral-100">{profile.company}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-neutral-700 dark:text-neutral-200 mb-1">
                    Location
                  </label>
                  {isEditing ? (
                    <input
                      type="text"
                      value={editedProfile.location}
                      onChange={(e) => setEditedProfile(prev => ({ ...prev, location: e.target.value }))}
                      className="w-full px-3 py-2 border border-neutral-300 dark:border-neutral-600 rounded-md bg-white dark:bg-neutral-800 text-neutral-900 dark:text-neutral-100"
                    />
                  ) : (
                    <p className="text-neutral-900 dark:text-neutral-100">{profile.location}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-neutral-700 dark:text-neutral-200 mb-1">
                    Timezone
                  </label>
                  {isEditing ? (
                    <select
                      value={editedProfile.timezone}
                      onChange={(e) => setEditedProfile(prev => ({ ...prev, timezone: e.target.value }))}
                      className="w-full px-3 py-2 border border-neutral-300 dark:border-neutral-600 rounded-md bg-white dark:bg-neutral-800 text-neutral-900 dark:text-neutral-100"
                    >
                      <option value="Pacific Standard Time (PST)">Pacific Standard Time (PST)</option>
                      <option value="Mountain Standard Time (MST)">Mountain Standard Time (MST)</option>
                      <option value="Central Standard Time (CST)">Central Standard Time (CST)</option>
                      <option value="Eastern Standard Time (EST)">Eastern Standard Time (EST)</option>
                      <option value="Greenwich Mean Time (GMT)">Greenwich Mean Time (GMT)</option>
                    </select>
                  ) : (
                    <p className="text-neutral-900 dark:text-neutral-100">{profile.timezone}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-neutral-700 dark:text-neutral-200 mb-1">
                    Last Login
                  </label>
                  <p className="text-neutral-900 dark:text-neutral-100">{profile.lastLogin}</p>
                </div>
              </div>
            </div>

            {/* Security Settings */}
            <div className="bg-white dark:bg-neutral-900 rounded-lg shadow-sm border border-neutral-200 dark:border-neutral-800 p-6">
              <h3 className="text-lg font-semibold text-neutral-800 dark:text-neutral-100 mb-4">Security Settings</h3>

              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="font-medium text-neutral-800 dark:text-neutral-100">Two-Factor Authentication</h4>
                    <p className="text-sm text-neutral-600 dark:text-neutral-300">Add an extra layer of security to your account</p>
                  </div>
                  <div className="flex items-center">
                    {profile.twoFactorEnabled ? (
                      <CheckCircle className="w-5 h-5 text-green-500 mr-2" />
                    ) : (
                      <AlertCircle className="w-5 h-5 text-red-500 mr-2" />
                    )}
                    <button
                      onClick={() => setProfile(prev => ({ ...prev, twoFactorEnabled: !prev.twoFactorEnabled }))}
                      className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                        profile.twoFactorEnabled ? 'bg-primary-600' : 'bg-neutral-200 dark:bg-neutral-700'
                      }`}
                    >
                      <span
                        className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                          profile.twoFactorEnabled ? 'translate-x-6' : 'translate-x-1'
                        }`}
                      />
                    </button>
                  </div>
                </div>

                <div className="border-t border-neutral-200 dark:border-neutral-700 pt-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <h4 className="font-medium text-neutral-800 dark:text-neutral-100">Change Password</h4>
                      <p className="text-sm text-neutral-600 dark:text-neutral-300">Update your account password</p>
                    </div>
                    <button
                      onClick={() => setShowPasswordChange(!showPasswordChange)}
                      className="flex items-center text-primary-600 hover:text-primary-700 dark:text-primary-400"
                    >
                      <Key className="w-4 h-4 mr-1" />
                      Change
                    </button>
                  </div>

                  {showPasswordChange && (
                    <div className="mt-4 space-y-4 p-4 bg-neutral-50 dark:bg-neutral-800 rounded-lg">
                      <div>
                        <label className="block text-sm font-medium text-neutral-700 dark:text-neutral-200 mb-1">
                          Current Password
                        </label>
                        <div className="relative">
                          <input
                            type={showCurrentPassword ? "text" : "password"}
                            value={currentPassword}
                            onChange={(e) => setCurrentPassword(e.target.value)}
                            className="w-full px-3 py-2 pr-10 border border-neutral-300 dark:border-neutral-600 rounded-md bg-white dark:bg-neutral-700 text-neutral-900 dark:text-neutral-100"
                          />
                          <button
                            type="button"
                            onClick={() => setShowCurrentPassword(!showCurrentPassword)}
                            className="absolute inset-y-0 right-0 pr-3 flex items-center"
                          >
                            {showCurrentPassword ? (
                              <EyeOff className="h-4 w-4 text-neutral-400" />
                            ) : (
                              <Eye className="h-4 w-4 text-neutral-400" />
                            )}
                          </button>
                        </div>
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-neutral-700 dark:text-neutral-200 mb-1">
                          New Password
                        </label>
                        <div className="relative">
                          <input
                            type={showNewPassword ? "text" : "password"}
                            value={newPassword}
                            onChange={(e) => setNewPassword(e.target.value)}
                            className="w-full px-3 py-2 pr-10 border border-neutral-300 dark:border-neutral-600 rounded-md bg-white dark:bg-neutral-700 text-neutral-900 dark:text-neutral-100"
                          />
                          <button
                            type="button"
                            onClick={() => setShowNewPassword(!showNewPassword)}
                            className="absolute inset-y-0 right-0 pr-3 flex items-center"
                          >
                            {showNewPassword ? (
                              <EyeOff className="h-4 w-4 text-neutral-400" />
                            ) : (
                              <Eye className="h-4 w-4 text-neutral-400" />
                            )}
                          </button>
                        </div>
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-neutral-700 dark:text-neutral-200 mb-1">
                          Confirm New Password
                        </label>
                        <div className="relative">
                          <input
                            type={showConfirmPassword ? "text" : "password"}
                            value={confirmPassword}
                            onChange={(e) => setConfirmPassword(e.target.value)}
                            className="w-full px-3 py-2 pr-10 border border-neutral-300 dark:border-neutral-600 rounded-md bg-white dark:bg-neutral-700 text-neutral-900 dark:text-neutral-100"
                          />
                          <button
                            type="button"
                            onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                            className="absolute inset-y-0 right-0 pr-3 flex items-center"
                          >
                            {showConfirmPassword ? (
                              <EyeOff className="h-4 w-4 text-neutral-400" />
                            ) : (
                              <Eye className="h-4 w-4 text-neutral-400" />
                            )}
                          </button>
                        </div>
                      </div>

                      <div className="flex space-x-3">
                        <button
                          onClick={handlePasswordChange}
                          className="px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700 transition-colors"
                        >
                          Update Password
                        </button>
                        <button
                          onClick={() => {
                            setShowPasswordChange(false);
                            setCurrentPassword('');
                            setNewPassword('');
                            setConfirmPassword('');
                          }}
                          className="px-4 py-2 bg-neutral-200 dark:bg-neutral-700 text-neutral-700 dark:text-neutral-200 rounded-md hover:bg-neutral-300 dark:hover:bg-neutral-600 transition-colors"
                        >
                          Cancel
                        </button>
                      </div>
                    </div>
                  )}
                </div>

                <div className="border-t border-neutral-200 dark:border-neutral-700 pt-4">
                  <h4 className="font-medium text-neutral-800 dark:text-neutral-100 mb-2">Permissions</h4>
                  <div className="flex flex-wrap gap-2">
                    {profile.permissions.map((permission, index) => (
                      <span
                        key={index}
                        className="px-3 py-1 bg-primary-100 dark:bg-primary-900 text-primary-700 dark:text-primary-300 rounded-full text-sm"
                      >
                        {permission}
                      </span>
                    ))}
                  </div>
                </div>
              </div>
            </div>

            {/* Notification Preferences */}
            <div className="bg-white dark:bg-neutral-900 rounded-lg shadow-sm border border-neutral-200 dark:border-neutral-800 p-6">
              <h3 className="text-lg font-semibold text-neutral-800 dark:text-neutral-100 mb-4">Notification Preferences</h3>

              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <Mail className="w-5 h-5 text-neutral-400 mr-3" />
                    <div>
                      <h4 className="font-medium text-neutral-800 dark:text-neutral-100">Email Notifications</h4>
                      <p className="text-sm text-neutral-600 dark:text-neutral-300">Receive notifications via email</p>
                    </div>
                  </div>
                  <button
                    onClick={() => handleNotificationChange('emailNotifications', !profile.emailNotifications)}
                    className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                      profile.emailNotifications ? 'bg-primary-600' : 'bg-neutral-200 dark:bg-neutral-700'
                    }`}
                  >
                    <span
                      className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                        profile.emailNotifications ? 'translate-x-6' : 'translate-x-1'
                      }`}
                    />
                  </button>
                </div>

                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <Phone className="w-5 h-5 text-neutral-400 mr-3" />
                    <div>
                      <h4 className="font-medium text-neutral-800 dark:text-neutral-100">SMS Notifications</h4>
                      <p className="text-sm text-neutral-600 dark:text-neutral-300">Receive notifications via SMS</p>
                    </div>
                  </div>
                  <button
                    onClick={() => handleNotificationChange('smsNotifications', !profile.smsNotifications)}
                    className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                      profile.smsNotifications ? 'bg-primary-600' : 'bg-neutral-200 dark:bg-neutral-700'
                    }`}
                  >
                    <span
                      className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                        profile.smsNotifications ? 'translate-x-6' : 'translate-x-1'
                      }`}
                    />
                  </button>
                </div>

                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <Shield className="w-5 h-5 text-neutral-400 mr-3" />
                    <div>
                      <h4 className="font-medium text-neutral-800 dark:text-neutral-100">Security Alerts</h4>
                      <p className="text-sm text-neutral-600 dark:text-neutral-300">Critical security notifications</p>
                    </div>
                  </div>
                  <button
                    onClick={() => handleNotificationChange('securityAlerts', !profile.securityAlerts)}
                    className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                      profile.securityAlerts ? 'bg-primary-600' : 'bg-neutral-200 dark:bg-neutral-700'
                    }`}
                  >
                    <span
                      className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                        profile.securityAlerts ? 'translate-x-6' : 'translate-x-1'
                      }`}
                    />
                  </button>
                </div>

                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <Bell className="w-5 h-5 text-neutral-400 mr-3" />
                    <div>
                      <h4 className="font-medium text-neutral-800 dark:text-neutral-100">Weekly Reports</h4>
                      <p className="text-sm text-neutral-600 dark:text-neutral-300">Weekly security summary reports</p>
                    </div>
                  </div>
                  <button
                    onClick={() => handleNotificationChange('weeklyReports', !profile.weeklyReports)}
                    className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                      profile.weeklyReports ? 'bg-primary-600' : 'bg-neutral-200 dark:bg-neutral-700'
                    }`}
                  >
                    <span
                      className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                        profile.weeklyReports ? 'translate-x-6' : 'translate-x-1'
                      }`}
                    />
                  </button>
                </div>
              </div>
            </div>


          </div>
        </div>
      </div>
    </div>
  );
};

export default Profile;
